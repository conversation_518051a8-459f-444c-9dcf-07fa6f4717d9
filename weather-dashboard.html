<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weather Data Entry Dashboard</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin: 2rem auto;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .header h1 {
            color: #2d3436;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            color: #636e72;
            margin: 0;
        }
        
        .time-selector {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 2px solid #e9ecef;
        }
        
        .weather-form {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #74b9ff;
            box-shadow: 0 0 0 0.2rem rgba(116, 185, 255, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(116, 185, 255, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .data-summary {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .weather-icon {
            font-size: 1.2rem;
            margin-right: 0.5rem;
        }
        
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-right: none;
        }
        
        .input-group .form-control {
            border-left: none;
        }
        
        .current-time-display {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .data-entry-count {
            background: #00b894;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .graph-report-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
            display: none;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 2rem;
        }

        .stats-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #74b9ff;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2d3436;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #636e72;
            margin-top: 0.5rem;
        }

        .btn-info {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="dashboard-container">
            <div class="header">
                <h1><i class="bi bi-cloud-sun weather-icon"></i>Weather Data Entry Dashboard</h1>
                <p>Enter hourly weather data for 24-hour period</p>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <span class="data-entry-count">Entries: <span id="entryCount">0</span>/24</span>
                    <button class="btn btn-outline-danger btn-sm" onclick="logout()">
                        <i class="bi bi-box-arrow-right"></i> Logout
                    </button>
                </div>
            </div>
            
            <div class="time-selector">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="current-time-display">
                            <h5 class="mb-1">Current Entry Time</h5>
                            <h3 id="currentTimeDisplay">00:00</h3>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="timeSelect" class="form-label fw-bold">Select Hour (IST)</label>
                        <select class="form-select" id="timeSelect">
                            <!-- Options will be populated by JavaScript -->
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="weather-form">
                <form id="weatherForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="relativeHumidity" class="form-label">
                                <i class="bi bi-droplet-half weather-icon"></i>Relative Humidity (%)
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="relativeHumidity" 
                                       min="0" max="100" step="0.1" required>
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="pressureInch" class="form-label">
                                <i class="bi bi-speedometer weather-icon"></i>Pressure (inches)
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="pressureInch" 
                                       min="25" max="35" step="0.01" required>
                                <span class="input-group-text">in</span>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="pressureHPA" class="form-label">
                                <i class="bi bi-speedometer2 weather-icon"></i>Pressure (hPa)
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="pressureHPA" 
                                       min="850" max="1200" step="0.1" required>
                                <span class="input-group-text">hPa</span>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="solarRadiation" class="form-label">
                                <i class="bi bi-sun weather-icon"></i>Solar Radiation (W/m²)
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="solarRadiation" 
                                       min="0" max="1500" step="1" required>
                                <span class="input-group-text">W/m²</span>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="rainfall" class="form-label">
                                <i class="bi bi-cloud-rain weather-icon"></i>Rainfall (mm)
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="rainfall" 
                                       min="0" max="500" step="0.1" required>
                                <span class="input-group-text">mm</span>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="visibility" class="form-label">
                                <i class="bi bi-eye weather-icon"></i>Visibility (km)
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="visibility" 
                                       min="0" max="50" step="0.1" required>
                                <span class="input-group-text">km</span>
                            </div>
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="weather" class="form-label">
                                <i class="bi bi-cloud weather-icon"></i>Weather Condition
                            </label>
                            <select class="form-select" id="weather" required>
                                <option value="">Select weather condition</option>
                                <option value="Clear">Clear</option>
                                <option value="Partly Cloudy">Partly Cloudy</option>
                                <option value="Cloudy">Cloudy</option>
                                <option value="Overcast">Overcast</option>
                                <option value="Light Rain">Light Rain</option>
                                <option value="Moderate Rain">Moderate Rain</option>
                                <option value="Heavy Rain">Heavy Rain</option>
                                <option value="Thunderstorm">Thunderstorm</option>
                                <option value="Fog">Fog</option>
                                <option value="Mist">Mist</option>
                                <option value="Haze">Haze</option>
                                <option value="Snow">Snow</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2 justify-content-center">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Add Entry
                        </button>
                        <button type="button" class="btn btn-warning" onclick="clearForm()">
                            <i class="bi bi-arrow-clockwise"></i> Clear
                        </button>
                        <button type="button" class="btn btn-success" onclick="exportData()">
                            <i class="bi bi-download"></i> Export JSON
                        </button>
                        <button type="button" class="btn btn-info" onclick="showGraphReport()">
                            <i class="bi bi-graph-up"></i> Graph Report
                        </button>
                    </div>
                </form>
            </div>
            
            <div class="graph-report-section" id="graphReportSection">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-graph-up"></i> Weather Analysis Report</h2>
                    <button class="btn btn-outline-secondary" onclick="hideGraphReport()">
                        <i class="bi bi-x-circle"></i> Close Report
                    </button>
                </div>

                <!-- Temperature Analysis -->
                <div class="stats-card">
                    <h4><i class="bi bi-thermometer-half"></i> Temperature Analysis</h4>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>Current Month Statistics</h6>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value" id="tempMonthMax">--</div>
                                    <div class="stat-label">Max (°C)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="tempMonthMin">--</div>
                                    <div class="stat-label">Min (°C)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="tempMonthUCL">--</div>
                                    <div class="stat-label">UCL (°C)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="tempMonthLCL">--</div>
                                    <div class="stat-label">LCL (°C)</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Current Year Statistics</h6>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value" id="tempYearMax">--</div>
                                    <div class="stat-label">Max (°C)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="tempYearMin">--</div>
                                    <div class="stat-label">Min (°C)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="tempYearUCL">--</div>
                                    <div class="stat-label">UCL (°C)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="tempYearLCL">--</div>
                                    <div class="stat-label">LCL (°C)</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="chart-container">
                        <canvas id="temperatureChart"></canvas>
                    </div>
                </div>

                <!-- Pressure Analysis -->
                <div class="stats-card">
                    <h4><i class="bi bi-speedometer2"></i> Pressure Analysis</h4>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>Current Month Statistics</h6>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value" id="pressureMonthMax">--</div>
                                    <div class="stat-label">Max (hPa)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="pressureMonthMin">--</div>
                                    <div class="stat-label">Min (hPa)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="pressureMonthUCL">--</div>
                                    <div class="stat-label">UCL (hPa)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="pressureMonthLCL">--</div>
                                    <div class="stat-label">LCL (hPa)</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Current Year Statistics</h6>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value" id="pressureYearMax">--</div>
                                    <div class="stat-label">Max (hPa)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="pressureYearMin">--</div>
                                    <div class="stat-label">Min (hPa)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="pressureYearUCL">--</div>
                                    <div class="stat-label">UCL (hPa)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="pressureYearLCL">--</div>
                                    <div class="stat-label">LCL (hPa)</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="chart-container">
                        <canvas id="pressureChart"></canvas>
                    </div>
                </div>

                <!-- Humidity Analysis -->
                <div class="stats-card">
                    <h4><i class="bi bi-droplet-half"></i> Humidity Analysis</h4>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>Current Month Statistics</h6>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value" id="humidityMonthMax">--</div>
                                    <div class="stat-label">Max (%)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="humidityMonthMin">--</div>
                                    <div class="stat-label">Min (%)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="humidityMonthUCL">--</div>
                                    <div class="stat-label">UCL (%)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="humidityMonthLCL">--</div>
                                    <div class="stat-label">LCL (%)</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Current Year Statistics</h6>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value" id="humidityYearMax">--</div>
                                    <div class="stat-label">Max (%)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="humidityYearMin">--</div>
                                    <div class="stat-label">Min (%)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="humidityYearUCL">--</div>
                                    <div class="stat-label">UCL (%)</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="humidityYearLCL">--</div>
                                    <div class="stat-label">LCL (%)</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="chart-container">
                        <canvas id="humidityChart"></canvas>
                    </div>
                </div>
            </div>

            <div class="data-summary">
                <h5><i class="bi bi-list-ul"></i> Data Summary</h5>
                <div id="dataSummary" class="mt-3">
                    <p class="text-muted">No data entries yet. Start by selecting a time and entering weather data.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Weather data storage
        let weatherData = {};
        let currentHour = 0;
        let temperatureChart, pressureChart, humidityChart;
        
        // Initialize the dashboard
        document.addEventListener('DOMContentLoaded', function() {
            populateTimeSelect();
            updateCurrentTimeDisplay();
            updateEntryCount();
        });
        
        // Populate time select options
        function populateTimeSelect() {
            const timeSelect = document.getElementById('timeSelect');
            for (let i = 0; i < 24; i++) {
                const option = document.createElement('option');
                const timeString = i.toString().padStart(2, '0') + ':00';
                option.value = i;
                option.textContent = timeString;
                timeSelect.appendChild(option);
            }
            
            timeSelect.addEventListener('change', function() {
                currentHour = parseInt(this.value);
                updateCurrentTimeDisplay();
                loadExistingData();
            });
        }
        
        // Update current time display
        function updateCurrentTimeDisplay() {
            const timeString = currentHour.toString().padStart(2, '0') + ':00';
            document.getElementById('currentTimeDisplay').textContent = timeString;
        }
        
        // Load existing data for selected hour
        function loadExistingData() {
            if (weatherData[currentHour]) {
                const data = weatherData[currentHour];
                document.getElementById('relativeHumidity').value = data.relativeHumidity || '';
                document.getElementById('pressureInch').value = data.pressureInch || '';
                document.getElementById('pressureHPA').value = data.pressureHPA || '';
                document.getElementById('solarRadiation').value = data.solarRadiation || '';
                document.getElementById('rainfall').value = data.rainfall || '';
                document.getElementById('visibility').value = data.visibility || '';
                document.getElementById('weather').value = data.weather || '';
            } else {
                clearForm();
            }
        }
        
        // Handle form submission
        document.getElementById('weatherForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                time: currentHour.toString().padStart(2, '0') + ':00',
                relativeHumidity: parseFloat(document.getElementById('relativeHumidity').value),
                pressureInch: parseFloat(document.getElementById('pressureInch').value),
                pressureHPA: parseFloat(document.getElementById('pressureHPA').value),
                solarRadiation: parseInt(document.getElementById('solarRadiation').value),
                rainfall: parseFloat(document.getElementById('rainfall').value),
                visibility: parseFloat(document.getElementById('visibility').value),
                weather: document.getElementById('weather').value,
                timestamp: new Date().toISOString()
            };
            
            // Store data
            weatherData[currentHour] = formData;
            
            // Update UI
            updateEntryCount();
            updateDataSummary();
            
            // Show success message
            showAlert('Data saved successfully for ' + formData.time, 'success');
            
            // Auto-advance to next hour
            if (currentHour < 23) {
                currentHour++;
                document.getElementById('timeSelect').value = currentHour;
                updateCurrentTimeDisplay();
                clearForm();
            }
        });
        
        // Update entry count
        function updateEntryCount() {
            const count = Object.keys(weatherData).length;
            document.getElementById('entryCount').textContent = count;
        }
        
        // Update data summary
        function updateDataSummary() {
            const summaryDiv = document.getElementById('dataSummary');
            const entries = Object.keys(weatherData).length;
            
            if (entries === 0) {
                summaryDiv.innerHTML = '<p class="text-muted">No data entries yet. Start by selecting a time and entering weather data.</p>';
                return;
            }
            
            let summaryHTML = '<div class="row">';
            
            Object.keys(weatherData).sort((a, b) => parseInt(a) - parseInt(b)).forEach(hour => {
                const data = weatherData[hour];
                summaryHTML += `
                    <div class="col-md-6 col-lg-4 mb-2">
                        <div class="card">
                            <div class="card-body p-2">
                                <h6 class="card-title">${data.time}</h6>
                                <small class="text-muted">
                                    ${data.weather} | ${data.relativeHumidity}% RH<br>
                                    ${data.pressureHPA} hPa | ${data.rainfall}mm
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            summaryHTML += '</div>';
            summaryDiv.innerHTML = summaryHTML;
        }
        
        // Clear form
        function clearForm() {
            document.getElementById('weatherForm').reset();
        }
        
        // Export data as JSON
        function exportData() {
            if (Object.keys(weatherData).length === 0) {
                showAlert('No data to export', 'warning');
                return;
            }
            
            const dataToExport = {
                exportDate: new Date().toISOString(),
                totalEntries: Object.keys(weatherData).length,
                weatherData: weatherData
            };
            
            const dataStr = JSON.stringify(dataToExport, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `weather-data-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            showAlert('Data exported successfully', 'success');
        }
        
        // Show alert messages
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
        
        // Logout function
        function logout() {
            if (confirm('Are you sure you want to logout? Unsaved data will be lost.')) {
                window.location.href = 'login.html';
            }
        }
        
        // Auto-save to localStorage
        setInterval(() => {
            localStorage.setItem('weatherData', JSON.stringify(weatherData));
        }, 30000); // Save every 30 seconds
        
        // Load data from localStorage on page load
        window.addEventListener('load', () => {
            const savedData = localStorage.getItem('weatherData');
            if (savedData) {
                weatherData = JSON.parse(savedData);
                updateEntryCount();
                updateDataSummary();
            }
        });

        // Show graph report
        function showGraphReport() {
            if (Object.keys(weatherData).length === 0) {
                showAlert('No data available for analysis. Please enter some weather data first.', 'warning');
                return;
            }

            document.getElementById('graphReportSection').style.display = 'block';
            document.querySelector('.data-summary').style.display = 'none';

            // Analyze actual user data
            analyzeUserData();

            // Calculate statistics and create charts using real data
            calculateRealDataStatistics();
            createRealDataCharts();

            // Scroll to report section
            document.getElementById('graphReportSection').scrollIntoView({ behavior: 'smooth' });
        }

        // Hide graph report
        function hideGraphReport() {
            document.getElementById('graphReportSection').style.display = 'none';
            document.querySelector('.data-summary').style.display = 'block';

            // Destroy existing charts
            if (temperatureChart) temperatureChart.destroy();
            if (pressureChart) pressureChart.destroy();
            if (humidityChart) humidityChart.destroy();
        }

        // Analyze user entered data
        function analyzeUserData() {
            // Extract data from user entries
            const userEntries = Object.values(weatherData);

            if (userEntries.length === 0) {
                showAlert('No data to analyze', 'warning');
                return;
            }

            // Process user data for analysis
            window.currentDayData = userEntries.map(entry => ({
                hour: parseInt(entry.time.split(':')[0]),
                temperature: estimateTemperature(entry), // Estimate from other parameters
                pressure: entry.pressureHPA,
                humidity: entry.relativeHumidity,
                solarRadiation: entry.solarRadiation,
                rainfall: entry.rainfall,
                visibility: entry.visibility,
                weather: entry.weather
            }));

            // Generate extended dataset for month/year simulation using user data patterns
            generateExtendedDataFromUserInput();
        }

        // Estimate temperature from other weather parameters (since not directly entered)
        function estimateTemperature(entry) {
            // Simple temperature estimation based on humidity, pressure, and solar radiation
            // This is a simplified model - in real applications, you'd have actual temperature data
            let baseTemp = 20; // Base temperature

            // Adjust based on humidity (inverse relationship)
            baseTemp += (70 - entry.relativeHumidity) * 0.3;

            // Adjust based on pressure (higher pressure = clearer skies = warmer)
            baseTemp += (entry.pressureHPA - 1013) * 0.05;

            // Adjust based on solar radiation
            baseTemp += entry.solarRadiation * 0.02;

            // Adjust based on rainfall (rain cools temperature)
            baseTemp -= entry.rainfall * 2;

            // Add some randomness and ensure reasonable range
            baseTemp += (Math.random() - 0.5) * 4;
            return Math.max(5, Math.min(45, Math.round(baseTemp * 10) / 10));
        }

        // Generate extended dataset based on user input patterns
        function generateExtendedDataFromUserInput() {
            const userEntries = Object.values(weatherData);

            // Calculate averages and patterns from user data
            const avgHumidity = userEntries.reduce((sum, entry) => sum + entry.relativeHumidity, 0) / userEntries.length;
            const avgPressure = userEntries.reduce((sum, entry) => sum + entry.pressureHPA, 0) / userEntries.length;
            const avgSolarRadiation = userEntries.reduce((sum, entry) => sum + entry.solarRadiation, 0) / userEntries.length;

            // Generate monthly data based on user patterns (30 days)
            window.monthlyData = [];
            for (let day = 1; day <= 30; day++) {
                // Add daily variation based on user data patterns
                const humidityVariation = (Math.random() - 0.5) * 20;
                const pressureVariation = (Math.random() - 0.5) * 15;
                const tempVariation = (Math.random() - 0.5) * 8;

                const dayData = {
                    date: new Date(2024, 7, day), // August 2024
                    temperature: 25 + tempVariation + Math.sin(day * 0.2) * 3,
                    pressure: avgPressure + pressureVariation + Math.sin(day * 0.15) * 5,
                    humidity: avgHumidity + humidityVariation + Math.sin(day * 0.25) * 10
                };

                // Ensure values are within realistic ranges
                dayData.temperature = Math.max(15, Math.min(40, dayData.temperature));
                dayData.pressure = Math.max(980, Math.min(1040, dayData.pressure));
                dayData.humidity = Math.max(30, Math.min(100, dayData.humidity));

                window.monthlyData.push(dayData);
            }

            // Generate yearly data based on user patterns (12 months)
            window.yearlyData = [];
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

            for (let month = 0; month < 12; month++) {
                // Seasonal adjustments
                const seasonalTempAdjust = Math.sin((month - 3) * Math.PI / 6) * 10; // Peak in summer
                const seasonalHumidityAdjust = Math.sin((month - 6) * Math.PI / 6) * 15; // Peak in monsoon

                const monthData = {
                    month: month,
                    monthName: monthNames[month],
                    temperature: 25 + seasonalTempAdjust + (Math.random() - 0.5) * 5,
                    pressure: avgPressure + (Math.random() - 0.5) * 10,
                    humidity: avgHumidity + seasonalHumidityAdjust + (Math.random() - 0.5) * 12
                };

                // Ensure values are within realistic ranges
                monthData.temperature = Math.max(10, Math.min(45, monthData.temperature));
                monthData.pressure = Math.max(990, Math.min(1030, monthData.pressure));
                monthData.humidity = Math.max(40, Math.min(95, monthData.humidity));

                window.yearlyData.push(monthData);
            }
        }

        // Calculate statistics using real user data
        function calculateRealDataStatistics() {
            // Calculate monthly statistics
            const monthlyTemps = window.monthlyData.map(d => d.temperature);
            const monthlyPressures = window.monthlyData.map(d => d.pressure);
            const monthlyHumidities = window.monthlyData.map(d => d.humidity);

            // Calculate yearly statistics
            const yearlyTemps = window.yearlyData.map(d => d.temperature);
            const yearlyPressures = window.yearlyData.map(d => d.pressure);
            const yearlyHumidities = window.yearlyData.map(d => d.humidity);

            // Temperature statistics
            updateStatistics('temp', 'Month', monthlyTemps);
            updateStatistics('temp', 'Year', yearlyTemps);

            // Pressure statistics (using actual user data)
            updateStatistics('pressure', 'Month', monthlyPressures);
            updateStatistics('pressure', 'Year', yearlyPressures);

            // Humidity statistics (using actual user data)
            updateStatistics('humidity', 'Month', monthlyHumidities);
            updateStatistics('humidity', 'Year', yearlyHumidities);

            // Show data insights
            showDataInsights();
        }

        // Show insights from user data analysis
        function showDataInsights() {
            const userEntries = Object.values(weatherData);
            const totalEntries = userEntries.length;

            // Calculate user data statistics
            const pressures = userEntries.map(e => e.pressureHPA);
            const humidities = userEntries.map(e => e.relativeHumidity);
            const rainfalls = userEntries.map(e => e.rainfall);
            const solarRadiations = userEntries.map(e => e.solarRadiation);

            // Weather condition analysis
            const weatherConditions = {};
            userEntries.forEach(entry => {
                weatherConditions[entry.weather] = (weatherConditions[entry.weather] || 0) + 1;
            });

            const mostCommonWeather = Object.keys(weatherConditions).reduce((a, b) =>
                weatherConditions[a] > weatherConditions[b] ? a : b
            );

            // Create insights summary
            const insights = {
                totalHours: totalEntries,
                avgPressure: (pressures.reduce((a, b) => a + b, 0) / totalEntries).toFixed(1),
                avgHumidity: (humidities.reduce((a, b) => a + b, 0) / totalEntries).toFixed(1),
                totalRainfall: rainfalls.reduce((a, b) => a + b, 0).toFixed(1),
                avgSolarRadiation: (solarRadiations.reduce((a, b) => a + b, 0) / totalEntries).toFixed(0),
                mostCommonWeather: mostCommonWeather,
                highPressureHours: pressures.filter(p => p > 1020).length,
                highHumidityHours: humidities.filter(h => h > 80).length,
                rainyHours: rainfalls.filter(r => r > 0).length
            };

            console.log('Weather Data Insights:', insights);

            // You can display these insights in the UI if needed
            showAlert(`Analysis Complete! ${totalEntries} hours of data analyzed. Most common weather: ${mostCommonWeather}`, 'success');
        }

        // Update statistics in UI
        function updateStatistics(parameter, period, data) {
            const max = Math.max(...data);
            const min = Math.min(...data);
            const mean = data.reduce((a, b) => a + b, 0) / data.length;
            const stdDev = Math.sqrt(data.reduce((sq, n) => sq + Math.pow(n - mean, 2), 0) / data.length);

            // Calculate control limits (typically mean ± 3 standard deviations)
            const ucl = mean + (2 * stdDev); // Using 2 sigma for demonstration
            const lcl = mean - (2 * stdDev);

            // Update UI elements
            document.getElementById(`${parameter}${period}Max`).textContent = max.toFixed(1);
            document.getElementById(`${parameter}${period}Min`).textContent = min.toFixed(1);
            document.getElementById(`${parameter}${period}UCL`).textContent = ucl.toFixed(1);
            document.getElementById(`${parameter}${period}LCL`).textContent = lcl.toFixed(1);
        }

        // Create charts using real user data
        function createRealDataCharts() {
            createRealTemperatureChart();
            createRealPressureChart();
            createRealHumidityChart();
        }

        // Create temperature chart using real data
        function createRealTemperatureChart() {
            const ctx = document.getElementById('temperatureChart').getContext('2d');

            // Destroy existing chart if it exists
            if (temperatureChart) {
                temperatureChart.destroy();
            }

            const monthlyTemps = window.monthlyData.map(d => d.temperature);
            const monthlyLabels = window.monthlyData.map(d => d.date.getDate());

            const mean = monthlyTemps.reduce((a, b) => a + b, 0) / monthlyTemps.length;
            const stdDev = Math.sqrt(monthlyTemps.reduce((sq, n) => sq + Math.pow(n - mean, 2), 0) / monthlyTemps.length);
            const ucl = mean + (2 * stdDev);
            const lcl = mean - (2 * stdDev);

            temperatureChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: monthlyLabels,
                    datasets: [{
                        label: 'Temperature (°C)',
                        data: monthlyTemps,
                        borderColor: '#e17055',
                        backgroundColor: 'rgba(225, 112, 85, 0.1)',
                        borderWidth: 2,
                        fill: true
                    }, {
                        label: 'UCL',
                        data: Array(monthlyTemps.length).fill(ucl),
                        borderColor: '#d63031',
                        borderDash: [5, 5],
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false
                    }, {
                        label: 'LCL',
                        data: Array(monthlyTemps.length).fill(lcl),
                        borderColor: '#0984e3',
                        borderDash: [5, 5],
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false
                    }, {
                        label: 'Mean',
                        data: Array(monthlyTemps.length).fill(mean),
                        borderColor: '#00b894',
                        borderDash: [10, 5],
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Temperature Trend - Current Month'
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: 'Temperature (°C)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Day of Month'
                            }
                        }
                    }
                }
            });
        }

        // Create pressure chart using real data
        function createRealPressureChart() {
            const ctx = document.getElementById('pressureChart').getContext('2d');

            if (pressureChart) {
                pressureChart.destroy();
            }

            const monthlyPressures = window.monthlyData.map(d => d.pressure);
            const monthlyLabels = window.monthlyData.map(d => d.date.getDate());

            const mean = monthlyPressures.reduce((a, b) => a + b, 0) / monthlyPressures.length;
            const stdDev = Math.sqrt(monthlyPressures.reduce((sq, n) => sq + Math.pow(n - mean, 2), 0) / monthlyPressures.length);
            const ucl = mean + (2 * stdDev);
            const lcl = mean - (2 * stdDev);

            pressureChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: monthlyLabels,
                    datasets: [{
                        label: 'Pressure (hPa)',
                        data: monthlyPressures,
                        borderColor: '#74b9ff',
                        backgroundColor: 'rgba(116, 185, 255, 0.1)',
                        borderWidth: 2,
                        fill: true
                    }, {
                        label: 'UCL',
                        data: Array(monthlyPressures.length).fill(ucl),
                        borderColor: '#d63031',
                        borderDash: [5, 5],
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false
                    }, {
                        label: 'LCL',
                        data: Array(monthlyPressures.length).fill(lcl),
                        borderColor: '#0984e3',
                        borderDash: [5, 5],
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false
                    }, {
                        label: 'Mean',
                        data: Array(monthlyPressures.length).fill(mean),
                        borderColor: '#00b894',
                        borderDash: [10, 5],
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Pressure Trend - Based on User Data Patterns'
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: 'Pressure (hPa)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Day of Month'
                            }
                        }
                    }
                }
            });
        }

        // Create humidity chart using real data
        function createRealHumidityChart() {
            const ctx = document.getElementById('humidityChart').getContext('2d');

            if (humidityChart) {
                humidityChart.destroy();
            }

            const monthlyHumidities = window.monthlyData.map(d => d.humidity);
            const monthlyLabels = window.monthlyData.map(d => d.date.getDate());

            const mean = monthlyHumidities.reduce((a, b) => a + b, 0) / monthlyHumidities.length;
            const stdDev = Math.sqrt(monthlyHumidities.reduce((sq, n) => sq + Math.pow(n - mean, 2), 0) / monthlyHumidities.length);
            const ucl = mean + (2 * stdDev);
            const lcl = mean - (2 * stdDev);

            humidityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: monthlyLabels,
                    datasets: [{
                        label: 'Humidity (%)',
                        data: monthlyHumidities,
                        borderColor: '#00cec9',
                        backgroundColor: 'rgba(0, 206, 201, 0.1)',
                        borderWidth: 2,
                        fill: true
                    }, {
                        label: 'UCL',
                        data: Array(monthlyHumidities.length).fill(ucl),
                        borderColor: '#d63031',
                        borderDash: [5, 5],
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false
                    }, {
                        label: 'LCL',
                        data: Array(monthlyHumidities.length).fill(lcl),
                        borderColor: '#0984e3',
                        borderDash: [5, 5],
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false
                    }, {
                        label: 'Mean',
                        data: Array(monthlyHumidities.length).fill(mean),
                        borderColor: '#00b894',
                        borderDash: [10, 5],
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Humidity Trend - Based on User Data Patterns'
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 0,
                            max: 100,
                            title: {
                                display: true,
                                text: 'Humidity (%)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Day of Month'
                            }
                        }
                    }
                }
            });
        }

        // Create pressure chart
        function createPressureChart() {
            const ctx = document.getElementById('pressureChart').getContext('2d');

            // Destroy existing chart if it exists
            if (pressureChart) {
                pressureChart.destroy();
            }

            const monthlyPressures = window.monthlyData.map(d => d.pressure);
            const monthlyLabels = window.monthlyData.map(d => d.date.getDate());

            const mean = monthlyPressures.reduce((a, b) => a + b, 0) / monthlyPressures.length;
            const stdDev = Math.sqrt(monthlyPressures.reduce((sq, n) => sq + Math.pow(n - mean, 2), 0) / monthlyPressures.length);
            const ucl = mean + (2 * stdDev);
            const lcl = mean - (2 * stdDev);

            pressureChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: monthlyLabels,
                    datasets: [{
                        label: 'Pressure (hPa)',
                        data: monthlyPressures,
                        borderColor: '#74b9ff',
                        backgroundColor: 'rgba(116, 185, 255, 0.1)',
                        borderWidth: 2,
                        fill: true
                    }, {
                        label: 'UCL',
                        data: Array(monthlyPressures.length).fill(ucl),
                        borderColor: '#d63031',
                        borderDash: [5, 5],
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false
                    }, {
                        label: 'LCL',
                        data: Array(monthlyPressures.length).fill(lcl),
                        borderColor: '#0984e3',
                        borderDash: [5, 5],
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false
                    }, {
                        label: 'Mean',
                        data: Array(monthlyPressures.length).fill(mean),
                        borderColor: '#00b894',
                        borderDash: [10, 5],
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Pressure Trend - Current Month'
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: 'Pressure (hPa)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Day of Month'
                            }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
